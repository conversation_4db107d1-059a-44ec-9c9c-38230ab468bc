app:
  # binary name
  name: 'fcm-game_flash_cn'
  # debug or release
  mode: 'release'
  port: 8805
  max_multipart_memory: 8388608

secret:
  api_key:
    - 'test_api_key'
    - 'test_api_key2'
  data_mask_key: 'UwM7rL549KuHOcGAQcgdimRAEiZDz4'

log:
  # 日志级别
  level: "debug"
  # 文件存储位置
  filename: "runtime/logs/app.log"
  # 单位为MB
  max_size: 100
  # 保存天数
  max_age: 7
  # 最多保存多少个备份
  max_backups: 5
  # 压缩
  compress: false

# 数据库信息
database:
  host: 'localhost'
  port: 23306
  database: 'fcm-game_flash_cn'
  user: 'root'
  password: '123456'
  charset: 'utf8mb4'
  max_idle_conns: 500
  max_open_conns: 1000
  conn_max_lifetime: 30
  conn_max_idle_timeout: 20
  stdout: false

redis:
  host: '127.0.0.1'
  # 端口
  port: 6379
  # 数据库编号
  db: 0
  # 密码
  password:
  # 最大空闲连接数
  max_idle: 100
  # 最大连接数
  max_active: 2000
  # 空闲连接超时时间
  idle_timeout: 120
  # 等待
  wait: true

queue:
  driver: 'redis'

mongo:
  connect_string: '*********************************************************'
  connect_timeout: 10
  database: 'fcm_log'
  max_pool_size: 50
