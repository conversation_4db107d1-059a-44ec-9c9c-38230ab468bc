package pusher

import (
	"context"
	"encoding/json"
	"fcm-game_flash_cn/app/models"
	"fcm-game_flash_cn/app/service/cache_service"
	"fcm-game_flash_cn/app/service/nppa_service"
	"fcm-game_flash_cn/common/constant"
	"fcm-game_flash_cn/common/global"
	"fcm-game_flash_cn/pkg/nppa_fcm"
	"fmt"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"log"
	"sync"
	"time"
)

// 并发数量
var num = 1

// 每秒请求数(峰值为10)
var qps = 8

// 每行数据大小
var rowLen int64 = 100

type PusherTask struct {
}

func (p *PusherTask) Run() {
	members, err := p.getMembers()
	if err != nil {
		fmt.Printf("集合中没有数据\n")
		return
	}
	num = len(members) * qps
	var wg sync.WaitGroup
	for _, v := range members {
		for i := 1; i <= num; i++ {
			wg.Add(1)
			go p.Push(i, v, &wg)
		}
	}
	wg.Wait()
}

func (p *PusherTask) Push(i int, key string, wg *sync.WaitGroup) error {
	defer wg.Done()
	queueName := constant.OnlinePushQueue + ":" + key
	ctx := context.Background()

	var (
		c1 *redis.StringSliceCmd
		c2 *redis.StatusCmd
		c3 *redis.IntCmd
	)
	_, err := global.Redis.TxPipelined(ctx, func(pip redis.Pipeliner) error {
		c1 = pip.LRange(ctx, queueName, 0, rowLen-1)
		if c1.Err() != nil {
			return c1.Err()
		}
		c2 = pip.LTrim(ctx, queueName, rowLen, -1)
		if c2.Err() != nil {
			return c2.Err()
		}
		c3 = pip.LLen(ctx, queueName)
		if c3.Err() != nil {
			return c3.Err()
		}
		return nil
	})

	if err != nil {
		return err
	}
	fmt.Printf("编号【%d】队列名称：%s, 本次获取队列数据条数：%d, 当前队列剩余长度：%d\n", i, queueName, len(c1.Val()), c3.Val())

	if len(c1.Val()) == 0 {
		// 无数据
		return nil
	}
	var (
		pushData []nppa_fcm.PushData
		gid      uint
	)
	number := 0
	for _, item := range c1.Val() {
		var obj models.OnlineRaw
		if err = json.Unmarshal([]byte(item), &obj); err != nil {
			// 解析失败
			continue
		}
		if obj.PI == "" {
			// 未实名
			continue
		}
		gid = obj.GID
		// https://wlc.nppa.gov.cn/2021/02/25/16e2520acd9f4404897ed1a5b8fd1240.pdf
		// 游客设备标识
		di := ""
		// 0为认证用户，2游客用户
		ct := 0
		// 行为时间戳
		ot := obj.Time
		now := time.Now().Unix()
		if obj.Time >= now {
			ot = now
		}
		if now-ot >= 180 {
			ot = now - 180
		}
		// 0 下线，1上线
		bt := 0
		if obj.OP == constant.OpOnline {
			bt = 1
		}
		number += 1
		pushData = append(pushData, nppa_fcm.PushData{
			NO: number,
			SI: obj.SSID,
			// 操作
			BT: bt,
			OT: ot,
			CT: ct,
			DI: di,
			PI: obj.PI,
		})
	}
	if len(pushData) <= 0 {
		// 暂无数据
		return nil
	}

	_, err = cache_service.GetFcmBizByGID(gid)
	if err != nil {
		err = fmt.Errorf("gid：%d配置不存在", gid)
		global.Log.Error("fcm.push.error", zap.Any("err", err))
		return err
	}
	// push
	f := nppa_service.New(&nppa_service.Options{GID: gid})
	resp, err := f.Push(pushData)
	if err != nil {
		global.Log.Error("fcm.push.error", zap.Any("request", pushData), zap.Any("result", resp), zap.Any("err", err))
		return err
	}
	return nil
}

func (p *PusherTask) getMembers() ([]string, error) {
	ctx := context.Background()
	strCmd := global.Redis.SMembers(ctx, constant.OnlineReadyPushMembers)
	if strCmd.Err() != nil {
		return nil, strCmd.Err()
	}
	var active []string
	for _, v := range strCmd.Val() {
		intCmd := global.Redis.LLen(ctx, constant.OnlinePushQueue+":"+v)
		if intCmd.Err() == nil && intCmd.Val() > 0 {
			active = append(active, v)
		}
	}
	return active, nil
}
