package message

import (
	"context"
	"errors"
	"fcm-game_flash_cn/app/dto"
	"fcm-game_flash_cn/app/models"
	"fcm-game_flash_cn/app/service/cache_service"
	"fcm-game_flash_cn/common/constant"
	"fcm-game_flash_cn/common/global"
	"fcm-game_flash_cn/pkg/queue"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"strings"
	"time"
)

type OnlineMessage struct {
	Name string
	Data *dto.OnlinePushReq
}

func (o *OnlineMessage) Resolve(data []byte) error {

	if err := jsoniter.Unmarshal(data, o.Data); err != nil {
		global.Log.Error("推送数据解析失败", zap.Any("data", data))
		return err
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	var err error
	filter := bson.D{
		{"ssid", o.Data.SSID},
		{"gid", o.Data.GID},
		{"op", o.Data.OP},
	}
	if len(o.Data.IP) > 0 && strings.Contains(o.Data.IP, ":") {
		ps := strings.Index(o.Data.IP, ":")
		if ps > -1 {
			o.Data.IP = o.Data.IP[:ps]
		}
	}
	rawLog := new(models.OnlineRaw)
	// 查重
	if err = global.Mongo.FindOne(ctx, "online_raw_log", filter).Decode(rawLog); err == nil {
		//global.Log.Warn("推送数据重复", zap.ByteString("data", data))
		return nil
	}
	// 获取用户信息
	fcmUser := new(models.FcmUser)
	if fcmUser, err = models.GetFcmUserByUid(o.Data.UID); err != nil {
		// 用户没有fcm user信息时，也插入在线记录
		rawLog.SSID = o.Data.SSID
		rawLog.SID = o.Data.SID
		rawLog.GID = o.Data.GID
		rawLog.IP = o.Data.IP
		rawLog.OP = o.Data.OP
		rawLog.DID = o.Data.DID
		rawLog.Time = o.Data.Time
		rawLog.UID = o.Data.UID
		rawLog.PI = ""

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		_, err = global.Mongo.InsertOne(ctx, "online_raw_log", rawLog)
		if err != nil {
			global.Log.Error("未实名用户日志写入失败", zap.Any("data", rawLog), zap.Error(err))
		}

		global.Log.Warn("用户未实名", zap.ByteString("data", data))
		return err
	}
	fcmData := fcmUser.FcmData
	rawLog.SSID = o.Data.SSID
	rawLog.SID = o.Data.SID
	rawLog.GID = o.Data.GID
	rawLog.IP = o.Data.IP
	rawLog.OP = o.Data.OP
	rawLog.DID = o.Data.DID
	rawLog.Time = o.Data.Time
	rawLog.UID = fcmUser.UID
	rawLog.PI = fcmData.PI
	result, err := global.Mongo.InsertOne(ctx, "online_raw_log", rawLog)
	if err != nil {
		global.Log.Error("原始日志写入失败", zap.Any("data", rawLog), zap.Error(err))
		return err
	}
	var ok bool
	if rawLog.Id, ok = result.InsertedID.(primitive.ObjectID); !ok {
		return errors.New("获取InsertID 失败")
	}
	// 配置
	biz, err := cache_service.GetFcmBizByGID(o.Data.GID)
	if err != nil {
		global.Log.Error("未找到配置", zap.Any("err", err), zap.Any("gid", o.Data.GID))
		return err
	}
	dataKey := fmt.Sprintf("account:%d:biz:%d", biz.AccountId, biz.Id)
	queueName := fmt.Sprintf(constant.OnlinePushQueue+":%s", dataKey)
	if err != nil {
		return err
	}
	reply, err := global.Redis.RPush(ctx, queueName, rawLog).Result()
	if err != nil {
		global.Log.Error("推送数据到队列失败", zap.Any("queue", queueName), zap.Any("err", err), zap.Any("reply", reply))
	} else {
		global.Redis.SAdd(ctx, constant.OnlineReadyPushMembers, dataKey)
		//global.Log.Info("推送数据到队列成功", zap.Any("queue", queueName), zap.Any("ssid", rawLog.SSID))
	}
	return nil
}

func (o *OnlineMessage) Channel() string {
	return o.Name
}

func (o *OnlineMessage) Marshal() ([]byte, error) {
	return jsoniter.Marshal(o.Data)
}

func (o *OnlineMessage) Unmarshal(reply []byte) (queue.IMessage, error) {
	var msg OnlineMessage
	err := jsoniter.Unmarshal(reply, &msg)
	return &msg, err
}
